using AMS.Shared.DTOs;
using AMS.Shared.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Text;
using System.Text.Json;

namespace AMS.Web.Controllers;

public class FlatsController : Controller
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public FlatsController(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        
        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7001";
        _httpClient.BaseAddress = new Uri(apiBaseUrl);
    }

    // GET: Flats/Details/5
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/flats/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var flat = JsonSerializer.Deserialize<FlatDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(flat);
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load flat details";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // GET: Flats/Create?blockId=5
    public async Task<IActionResult> Create(int blockId)
    {
        try
        {
            // Get block details to display context
            var blockResponse = await _httpClient.GetAsync($"/api/blocks/{blockId}");
            if (blockResponse.IsSuccessStatusCode)
            {
                var blockJson = await blockResponse.Content.ReadAsStringAsync();
                var block = JsonSerializer.Deserialize<BlockDto>(blockJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                ViewBag.Block = block;
            }
            
            var createDto = new CreateFlatDto { BlockId = blockId };
            SetupFlatTypeAndStatusDropdowns();
            return View(createDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            SetupFlatTypeAndStatusDropdowns();
            return View(new CreateFlatDto { BlockId = blockId });
        }
    }

    // POST: Flats/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateFlatDto createFlatDto)
    {
        if (!ModelState.IsValid)
        {
            // Reload block info for display
            try
            {
                var blockResponse = await _httpClient.GetAsync($"/api/blocks/{createFlatDto.BlockId}");
                if (blockResponse.IsSuccessStatusCode)
                {
                    var blockJson = await blockResponse.Content.ReadAsStringAsync();
                    var block = JsonSerializer.Deserialize<BlockDto>(blockJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    ViewBag.Block = block;
                }
            }
            catch { }
            
            SetupFlatTypeAndStatusDropdowns();
            return View(createFlatDto);
        }

        try
        {
            var json = JsonSerializer.Serialize(createFlatDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/flats", content);
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Flat created successfully";
                return RedirectToAction("Flats", "Blocks", new { id = createFlatDto.BlockId });
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            ViewBag.Error = $"Failed to create flat: {errorContent}";
            SetupFlatTypeAndStatusDropdowns();
            return View(createFlatDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            SetupFlatTypeAndStatusDropdowns();
            return View(createFlatDto);
        }
    }

    // GET: Flats/Edit/5
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/flats/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var flat = JsonSerializer.Deserialize<FlatDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                if (flat != null)
                {
                    var updateDto = new UpdateFlatDto
                    {
                        Number = flat.Number,
                        Type = flat.Type,
                        Area = flat.Area,
                        Floor = flat.Floor,
                        Status = flat.Status
                    };
                    ViewBag.FlatId = id;
                    ViewBag.BlockId = flat.BlockId;
                    ViewBag.BlockName = flat.BlockName;
                    ViewBag.SocietyName = flat.SocietyName;
                    SetupFlatTypeAndStatusDropdowns();
                    return View(updateDto);
                }
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load flat for editing";
            SetupFlatTypeAndStatusDropdowns();
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            SetupFlatTypeAndStatusDropdowns();
            return View();
        }
    }

    // POST: Flats/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, UpdateFlatDto updateFlatDto)
    {
        if (!ModelState.IsValid)
        {
            ViewBag.FlatId = id;
            SetupFlatTypeAndStatusDropdowns();
            return View(updateFlatDto);
        }

        try
        {
            var json = JsonSerializer.Serialize(updateFlatDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"/api/flats/{id}", content);
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Flat updated successfully";
                
                // Get block ID to redirect back to block flats
                var flatResponse = await _httpClient.GetAsync($"/api/flats/{id}");
                if (flatResponse.IsSuccessStatusCode)
                {
                    var flatJson = await flatResponse.Content.ReadAsStringAsync();
                    var flat = JsonSerializer.Deserialize<FlatDto>(flatJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return RedirectToAction("Flats", "Blocks", new { id = flat?.BlockId });
                }
                
                return RedirectToAction("Index", "Societies");
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            ViewBag.Error = $"Failed to update flat: {errorContent}";
            ViewBag.FlatId = id;
            SetupFlatTypeAndStatusDropdowns();
            return View(updateFlatDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            ViewBag.FlatId = id;
            SetupFlatTypeAndStatusDropdowns();
            return View(updateFlatDto);
        }
    }

    // GET: Flats/Delete/5
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/flats/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var flat = JsonSerializer.Deserialize<FlatDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(flat);
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load flat for deletion";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // POST: Flats/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            // Get block ID before deletion
            int? blockId = null;
            var flatResponse = await _httpClient.GetAsync($"/api/flats/{id}");
            if (flatResponse.IsSuccessStatusCode)
            {
                var flatJson = await flatResponse.Content.ReadAsStringAsync();
                var flat = JsonSerializer.Deserialize<FlatDto>(flatJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                blockId = flat?.BlockId;
            }
            
            var response = await _httpClient.DeleteAsync($"/api/flats/{id}");
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Flat deleted successfully";
                if (blockId.HasValue)
                {
                    return RedirectToAction("Flats", "Blocks", new { id = blockId.Value });
                }
                return RedirectToAction("Index", "Societies");
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            TempData["Error"] = $"Failed to delete flat: {errorContent}";
            return RedirectToAction(nameof(Delete), new { id });
        }
        catch (Exception ex)
        {
            TempData["Error"] = $"Error: {ex.Message}";
            return RedirectToAction(nameof(Delete), new { id });
        }
    }

    // GET: Flats/Search
    public async Task<IActionResult> Search()
    {
        try
        {
            // Load societies for dropdown
            var societiesResponse = await _httpClient.GetAsync("/api/societies");
            if (societiesResponse.IsSuccessStatusCode)
            {
                var societiesJson = await societiesResponse.Content.ReadAsStringAsync();
                var societies = JsonSerializer.Deserialize<List<SocietyDto>>(societiesJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                ViewBag.Societies = new SelectList(societies, "Id", "Name");
            }
            
            SetupFlatTypeAndStatusDropdowns();
            return View(new FlatSearchCriteria());
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            SetupFlatTypeAndStatusDropdowns();
            return View(new FlatSearchCriteria());
        }
    }

    // POST: Flats/Search
    [HttpPost]
    public async Task<IActionResult> Search(FlatSearchCriteria criteria)
    {
        try
        {
            var queryParams = new List<string>();
            
            if (criteria.SocietyId.HasValue)
                queryParams.Add($"societyId={criteria.SocietyId}");
            if (criteria.BlockId.HasValue)
                queryParams.Add($"blockId={criteria.BlockId}");
            if (criteria.Type.HasValue)
                queryParams.Add($"type={criteria.Type}");
            if (criteria.Status.HasValue)
                queryParams.Add($"status={criteria.Status}");
            if (criteria.Floor.HasValue)
                queryParams.Add($"floor={criteria.Floor}");
            if (criteria.MinArea.HasValue)
                queryParams.Add($"minArea={criteria.MinArea}");
            if (criteria.MaxArea.HasValue)
                queryParams.Add($"maxArea={criteria.MaxArea}");
            if (!string.IsNullOrEmpty(criteria.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(criteria.SearchTerm)}");
            
            var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
            var response = await _httpClient.GetAsync($"/api/flats/search{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var flats = JsonSerializer.Deserialize<List<FlatDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                ViewBag.SearchResults = flats ?? new List<FlatDto>();
            }
            else
            {
                ViewBag.Error = "Failed to search flats";
                ViewBag.SearchResults = new List<FlatDto>();
            }
            
            // Reload societies for dropdown
            var societiesResponse = await _httpClient.GetAsync("/api/societies");
            if (societiesResponse.IsSuccessStatusCode)
            {
                var societiesJson = await societiesResponse.Content.ReadAsStringAsync();
                var societies = JsonSerializer.Deserialize<List<SocietyDto>>(societiesJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                ViewBag.Societies = new SelectList(societies, "Id", "Name");
            }
            
            SetupFlatTypeAndStatusDropdowns();
            return View(criteria);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            ViewBag.SearchResults = new List<FlatDto>();
            SetupFlatTypeAndStatusDropdowns();
            return View(criteria);
        }
    }

    private void SetupFlatTypeAndStatusDropdowns()
    {
        ViewBag.FlatTypes = new SelectList(Enum.GetValues<FlatType>().Select(x => new { 
            Value = (int)x, 
            Text = x.ToString() 
        }), "Value", "Text");
        
        ViewBag.OccupancyStatuses = new SelectList(Enum.GetValues<OccupancyStatus>().Select(x => new { 
            Value = (int)x, 
            Text = x.ToString() 
        }), "Value", "Text");
    }
}
using AMS.API.Data;
using AMS.API.Models;
using AMS.Shared.DTOs;
using AMS.Shared.Enums;
using AMS.Shared.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace AMS.API.Services;

public class UserService : IUserService
{
    private readonly AmsDbContext _context;
    private readonly JwtService _jwtService;

    public UserService(AmsDbContext context, JwtService jwtService)
    {
        _context = context;
        _jwtService = jwtService;
    }

    public async Task<UserDto> CreateUserAsync(CreateUserDto dto)
    {
        // Check if email already exists
        var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Email == dto.Email);
        if (existingUser != null)
        {
            throw new ArgumentException("User with this email already exists");
        }

        var user = new User
        {
            Name = dto.Name,
            Email = dto.Email,
            PasswordHash = HashPassword(dto.Password),
            Phone = dto.Phone,
            Role = dto.Role,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        return MapUserToDto(user);
    }

    public async Task<UserDto?> GetUserByIdAsync(int id)
    {
        var user = await _context.Users
            .Include(u => u.UserFlats)
            .ThenInclude(uf => uf.Flat)
            .ThenInclude(f => f.Block)
            .ThenInclude(b => b.Society)
            .FirstOrDefaultAsync(u => u.Id == id);

        return user == null ? null : MapUserToDto(user);
    }

    public async Task<UserDto?> GetUserByEmailAsync(string email)
    {
        var user = await _context.Users
            .Include(u => u.UserFlats)
            .ThenInclude(uf => uf.Flat)
            .ThenInclude(f => f.Block)
            .ThenInclude(b => b.Society)
            .FirstOrDefaultAsync(u => u.Email == email);

        return user == null ? null : MapUserToDto(user);
    }

    public async Task<UserDto?> UpdateUserAsync(int id, UpdateUserDto dto)
    {
        var user = await _context.Users.FindAsync(id);
        if (user == null) return null;

        // Check if email is being changed and if it already exists
        if (user.Email != dto.Email)
        {
            var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Email == dto.Email && u.Id != id);
            if (existingUser != null)
            {
                throw new ArgumentException("User with this email already exists");
            }
        }

        user.Name = dto.Name;
        user.Email = dto.Email;
        user.Phone = dto.Phone;
        user.Role = dto.Role;
        user.IsActive = dto.IsActive;
        user.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return await GetUserByIdAsync(id);
    }

    public async Task<bool> AssignUserToFlatAsync(AssignUserToFlatDto dto)
    {
        // Verify user exists
        var user = await _context.Users.FindAsync(dto.UserId);
        if (user == null) return false;

        // Verify flat exists
        var flat = await _context.Flats.FindAsync(dto.FlatId);
        if (flat == null) return false;

        // Check if user is already assigned to this flat
        var existingAssignment = await _context.UserFlats
            .FirstOrDefaultAsync(uf => uf.UserId == dto.UserId && 
                                     uf.FlatId == dto.FlatId && 
                                     uf.EndDate == null);

        if (existingAssignment != null)
        {
            throw new ArgumentException("User is already assigned to this flat");
        }

        // If this is a primary contact, remove primary contact status from other residents
        if (dto.IsPrimaryContact)
        {
            var existingPrimaryContacts = await _context.UserFlats
                .Where(uf => uf.FlatId == dto.FlatId && uf.IsPrimaryContact && uf.EndDate == null)
                .ToListAsync();

            foreach (var contact in existingPrimaryContacts)
            {
                contact.IsPrimaryContact = false;
            }
        }

        var userFlat = new UserFlat
        {
            UserId = dto.UserId,
            FlatId = dto.FlatId,
            Type = dto.Type,
            IsPrimaryContact = dto.IsPrimaryContact,
            StartDate = dto.StartDate
        };

        _context.UserFlats.Add(userFlat);
        await _context.SaveChangesAsync();

        // Update flat occupancy status
        await UpdateFlatOccupancyStatus(dto.FlatId);

        return true;
    }

    public async Task<bool> RemoveUserFromFlatAsync(int userId, int flatId)
    {
        var userFlat = await _context.UserFlats
            .FirstOrDefaultAsync(uf => uf.UserId == userId && uf.FlatId == flatId && uf.EndDate == null);

        if (userFlat == null) return false;

        userFlat.EndDate = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        // Update flat occupancy status
        await UpdateFlatOccupancyStatus(flatId);

        return true;
    }

    public async Task<IEnumerable<UserDto>> GetFlatResidentsAsync(int flatId)
    {
        var userFlats = await _context.UserFlats
            .Include(uf => uf.User)
            .Where(uf => uf.FlatId == flatId && uf.EndDate == null)
            .ToListAsync();

        return userFlats.Select(uf => MapUserToDto(uf.User));
    }

    public async Task<AuthResponseDto?> AuthenticateAsync(LoginDto loginDto)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Email == loginDto.Email && u.IsActive);

        if (user == null || !VerifyPassword(loginDto.Password, user.PasswordHash))
        {
            return null;
        }

        var token = _jwtService.GenerateToken(user);
        
        return new AuthResponseDto
        {
            Token = token,
            User = MapUserToDto(user),
            ExpiresAt = DateTime.UtcNow.AddHours(24)
        };
    }

    public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
    {
        var users = await _context.Users
            .Include(u => u.UserFlats)
            .ThenInclude(uf => uf.Flat)
            .ThenInclude(f => f.Block)
            .ThenInclude(b => b.Society)
            .ToListAsync();

        return users.Select(MapUserToDto);
    }

    public async Task<bool> DeactivateUserAsync(int id)
    {
        var user = await _context.Users.FindAsync(id);
        if (user == null) return false;

        user.IsActive = false;
        user.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    #region Private Helper Methods

    private static UserDto MapUserToDto(User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Name = user.Name,
            Email = user.Email,
            Phone = user.Phone,
            Role = user.Role,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            AssignedFlats = user.UserFlats?.Where(uf => uf.EndDate == null).Select(uf => new UserFlatDto
            {
                Id = uf.Id,
                UserId = uf.UserId,
                FlatId = uf.FlatId,
                FlatNumber = uf.Flat?.Number ?? "",
                BlockName = uf.Flat?.Block?.Name ?? "",
                SocietyName = uf.Flat?.Block?.Society?.Name ?? "",
                Type = uf.Type,
                IsPrimaryContact = uf.IsPrimaryContact,
                StartDate = uf.StartDate,
                EndDate = uf.EndDate
            }).ToList() ?? new List<UserFlatDto>()
        };
    }

    private static string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        return Convert.ToBase64String(hashedBytes);
    }

    private static bool VerifyPassword(string password, string hash)
    {
        var hashedPassword = HashPassword(password);
        return hashedPassword == hash;
    }



    private async Task UpdateFlatOccupancyStatus(int flatId)
    {
        var flat = await _context.Flats.FindAsync(flatId);
        if (flat == null) return;

        var activeResidents = await _context.UserFlats
            .CountAsync(uf => uf.FlatId == flatId && uf.EndDate == null);

        flat.Status = activeResidents > 0 ? OccupancyStatus.Occupied : OccupancyStatus.Vacant;
        flat.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
    }

    #endregion
}
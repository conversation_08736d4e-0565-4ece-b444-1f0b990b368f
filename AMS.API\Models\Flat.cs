using System.ComponentModel.DataAnnotations;
using AMS.Shared.Enums;

namespace AMS.API.Models;

public class Flat
{
    public int Id { get; set; }

    [Required]
    [StringLength(20)]
    public string Number { get; set; } = string.Empty;

    [Required]
    public FlatType Type { get; set; }

    [Required]
    [Range(100, 10000)]
    public decimal Area { get; set; }

    [Required]
    [Range(0, 100)]
    public int Floor { get; set; }

    [Required]
    public OccupancyStatus Status { get; set; }

    [Required]
    public int BlockId { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public Block Block { get; set; } = null!;
    public ICollection<UserFlat> UserFlats { get; set; } = new List<UserFlat>();
}
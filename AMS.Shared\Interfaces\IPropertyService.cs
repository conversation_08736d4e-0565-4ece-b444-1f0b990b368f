using AMS.Shared.DTOs;

namespace AMS.Shared.Interfaces;

public interface IPropertyService
{
    // Society Management
    Task<IEnumerable<SocietyDto>> GetAllSocietiesAsync();
    Task<SocietyDto?> GetSocietyByIdAsync(int id);
    Task<SocietyDto> CreateSocietyAsync(CreateSocietyDto dto);
    Task<SocietyDto?> UpdateSocietyAsync(int id, UpdateSocietyDto dto);
    Task<bool> DeleteSocietyAsync(int id);
    
    // Block Management
    Task<IEnumerable<BlockDto>> GetBlocksBySocietyAsync(int societyId);
    Task<BlockDto?> GetBlockByIdAsync(int id);
    Task<BlockDto> CreateBlockAsync(CreateBlockDto dto);
    Task<BlockDto?> UpdateBlockAsync(int id, UpdateBlockDto dto);
    Task<bool> DeleteBlockAsync(int id);
    
    // Flat Management
    Task<IEnumerable<FlatDto>> GetFlatsByBlockAsync(int blockId);
    Task<FlatDto?> GetFlatByIdAsync(int id);
    Task<FlatDto> CreateFlatAsync(CreateFlatDto dto);
    Task<FlatDto?> UpdateFlatAsync(int id, UpdateFlatDto dto);
    Task<bool> DeleteFlatAsync(int id);
    Task<IEnumerable<FlatDto>> SearchFlatsAsync(FlatSearchCriteria criteria);
}
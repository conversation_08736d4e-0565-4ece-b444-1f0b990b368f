using AMS.API.Attributes;
using AMS.Shared.DTOs;
using AMS.Shared.Enums;
using AMS.Shared.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AMS.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;

    public UsersController(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// Get all users
    /// </summary>
    [HttpGet]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<IEnumerable<UserDto>>> GetAllUsers()
    {
        try
        {
            var users = await _userService.GetAllUsersAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving users", details = ex.Message });
        }
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize]
    public async Task<ActionResult<UserDto>> GetUser(int id)
    {
        try
        {
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = $"User with ID {id} not found" });
            }
            return Ok(user);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the user", details = ex.Message });
        }
    }

    /// <summary>
    /// Get user by email
    /// </summary>
    [HttpGet("by-email/{email}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<UserDto>> GetUserByEmail(string email)
    {
        try
        {
            var user = await _userService.GetUserByEmailAsync(email);
            if (user == null)
            {
                return NotFound(new { message = $"User with email {email} not found" });
            }
            return Ok(user);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the user", details = ex.Message });
        }
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    [HttpPost]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<UserDto>> CreateUser([FromBody] CreateUserDto createUserDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = await _userService.CreateUserAsync(createUserDto);
            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, user);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while creating the user", details = ex.Message });
        }
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    [HttpPut("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<UserDto>> UpdateUser(int id, [FromBody] UpdateUserDto updateUserDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var user = await _userService.UpdateUserAsync(id, updateUserDto);
            if (user == null)
            {
                return NotFound(new { message = $"User with ID {id} not found" });
            }

            return Ok(user);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while updating the user", details = ex.Message });
        }
    }

    /// <summary>
    /// Deactivate a user
    /// </summary>
    [HttpPatch("{id}/deactivate")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult> DeactivateUser(int id)
    {
        try
        {
            var result = await _userService.DeactivateUserAsync(id);
            if (!result)
            {
                return NotFound(new { message = $"User with ID {id} not found" });
            }

            return Ok(new { message = "User successfully deactivated" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while deactivating the user", details = ex.Message });
        }
    }

    /// <summary>
    /// Assign user to flat
    /// </summary>
    [HttpPost("{id}/assign-flat")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult> AssignUserToFlat(int id, [FromBody] AssignUserToFlatDto assignDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Ensure the user ID in the URL matches the DTO
            if (id != assignDto.UserId)
            {
                return BadRequest(new { message = "User ID in URL does not match the request body" });
            }

            var result = await _userService.AssignUserToFlatAsync(assignDto);
            if (!result)
            {
                return BadRequest(new { message = "Failed to assign user to flat. User or flat may not exist." });
            }

            return Ok(new { message = "User successfully assigned to flat" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while assigning user to flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Remove user from flat
    /// </summary>
    [HttpDelete("{userId}/remove-flat/{flatId}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult> RemoveUserFromFlat(int userId, int flatId)
    {
        try
        {
            var result = await _userService.RemoveUserFromFlatAsync(userId, flatId);
            if (!result)
            {
                return NotFound(new { message = "User assignment to flat not found" });
            }

            return Ok(new { message = "User successfully removed from flat" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while removing user from flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Authenticate user (login)
    /// </summary>
    [HttpPost("authenticate")]
    public async Task<ActionResult<AuthResponseDto>> Authenticate([FromBody] LoginDto loginDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var authResponse = await _userService.AuthenticateAsync(loginDto);
            if (authResponse == null)
            {
                return Unauthorized(new { message = "Invalid email or password" });
            }

            return Ok(authResponse);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred during authentication", details = ex.Message });
        }
    }
}
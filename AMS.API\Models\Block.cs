using System.ComponentModel.DataAnnotations;

namespace AMS.API.Models;

public class Block
{
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [Range(1, 100)]
    public int NumberOfFloors { get; set; }

    [Required]
    [Range(1, 1000)]
    public int TotalFlats { get; set; }

    [Required]
    public int SocietyId { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public Society Society { get; set; } = null!;
    public ICollection<Flat> Flats { get; set; } = new List<Flat>();
}
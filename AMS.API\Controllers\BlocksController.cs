using AMS.API.Attributes;
using AMS.Shared.DTOs;
using AMS.Shared.Enums;
using AMS.Shared.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AMS.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class BlocksController : ControllerBase
{
    private readonly IPropertyService _propertyService;

    public BlocksController(IPropertyService propertyService)
    {
        _propertyService = propertyService;
    }

    /// <summary>
    /// Get block by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<BlockDto>> GetBlock(int id)
    {
        try
        {
            var block = await _propertyService.GetBlockByIdAsync(id);
            if (block == null)
            {
                return NotFound(new { message = $"Block with ID {id} not found" });
            }
            return Ok(block);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the block", details = ex.Message });
        }
    }

    /// <summary>
    /// Create a new block
    /// </summary>
    [HttpPost]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<BlockDto>> CreateBlock([FromBody] CreateBlockDto createBlockDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var block = await _propertyService.CreateBlockAsync(createBlockDto);
            return CreatedAtAction(nameof(GetBlock), new { id = block.Id }, block);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while creating the block", details = ex.Message });
        }
    }

    /// <summary>
    /// Update an existing block
    /// </summary>
    [HttpPut("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<BlockDto>> UpdateBlock(int id, [FromBody] UpdateBlockDto updateBlockDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var block = await _propertyService.UpdateBlockAsync(id, updateBlockDto);
            if (block == null)
            {
                return NotFound(new { message = $"Block with ID {id} not found" });
            }

            return Ok(block);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while updating the block", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete a block
    /// </summary>
    [HttpDelete("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult> DeleteBlock(int id)
    {
        try
        {
            var result = await _propertyService.DeleteBlockAsync(id);
            if (!result)
            {
                return NotFound(new { message = $"Block with ID {id} not found" });
            }

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while deleting the block", details = ex.Message });
        }
    }

    /// <summary>
    /// Get flats for a specific block
    /// </summary>
    [HttpGet("{id}/flats")]
    public async Task<ActionResult<IEnumerable<FlatDto>>> GetBlockFlats(int id)
    {
        try
        {
            // First verify block exists
            var block = await _propertyService.GetBlockByIdAsync(id);
            if (block == null)
            {
                return NotFound(new { message = $"Block with ID {id} not found" });
            }

            var flats = await _propertyService.GetFlatsByBlockAsync(id);
            return Ok(flats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving block flats", details = ex.Message });
        }
    }
}
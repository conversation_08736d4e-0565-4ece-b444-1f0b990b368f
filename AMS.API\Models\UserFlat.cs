using System.ComponentModel.DataAnnotations;
using AMS.Shared.Enums;

namespace AMS.API.Models;

public class UserFlat
{
    public int Id { get; set; }

    [Required]
    public int UserId { get; set; }

    [Required]
    public int FlatId { get; set; }

    [Required]
    public ResidentType Type { get; set; }

    public bool IsPrimaryContact { get; set; } = false;

    public DateTime StartDate { get; set; } = DateTime.UtcNow;
    public DateTime? EndDate { get; set; }

    // Navigation Properties
    public User User { get; set; } = null!;
    public Flat Flat { get; set; } = null!;
}
using AMS.API.Attributes;
using AMS.Shared.DTOs;
using AMS.Shared.Enums;
using AMS.Shared.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AMS.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SocietiesController : ControllerBase
{
    private readonly IPropertyService _propertyService;

    public SocietiesController(IPropertyService propertyService)
    {
        _propertyService = propertyService;
    }

    /// <summary>
    /// Get all societies
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<SocietyDto>>> GetAllSocieties()
    {
        try
        {
            var societies = await _propertyService.GetAllSocietiesAsync();
            return Ok(societies);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving societies", details = ex.Message });
        }
    }

    /// <summary>
    /// Get society by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<SocietyDto>> GetSociety(int id)
    {
        try
        {
            var society = await _propertyService.GetSocietyByIdAsync(id);
            if (society == null)
            {
                return NotFound(new { message = $"Society with ID {id} not found" });
            }
            return Ok(society);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the society", details = ex.Message });
        }
    }

    /// <summary>
    /// Create a new society
    /// </summary>
    [HttpPost]
    [AuthorizeRole(UserRole.SuperAdmin)]
    public async Task<ActionResult<SocietyDto>> CreateSociety([FromBody] CreateSocietyDto createSocietyDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var society = await _propertyService.CreateSocietyAsync(createSocietyDto);
            return CreatedAtAction(nameof(GetSociety), new { id = society.Id }, society);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while creating the society", details = ex.Message });
        }
    }

    /// <summary>
    /// Update an existing society
    /// </summary>
    [HttpPut("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<SocietyDto>> UpdateSociety(int id, [FromBody] UpdateSocietyDto updateSocietyDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var society = await _propertyService.UpdateSocietyAsync(id, updateSocietyDto);
            if (society == null)
            {
                return NotFound(new { message = $"Society with ID {id} not found" });
            }

            return Ok(society);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while updating the society", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete a society
    /// </summary>
    [HttpDelete("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin)]
    public async Task<ActionResult> DeleteSociety(int id)
    {
        try
        {
            var result = await _propertyService.DeleteSocietyAsync(id);
            if (!result)
            {
                return NotFound(new { message = $"Society with ID {id} not found" });
            }

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while deleting the society", details = ex.Message });
        }
    }

    /// <summary>
    /// Get blocks for a specific society
    /// </summary>
    [HttpGet("{id}/blocks")]
    public async Task<ActionResult<IEnumerable<BlockDto>>> GetSocietyBlocks(int id)
    {
        try
        {
            // First verify society exists
            var society = await _propertyService.GetSocietyByIdAsync(id);
            if (society == null)
            {
                return NotFound(new { message = $"Society with ID {id} not found" });
            }

            var blocks = await _propertyService.GetBlocksBySocietyAsync(id);
            return Ok(blocks);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving society blocks", details = ex.Message });
        }
    }
}
using System.ComponentModel.DataAnnotations;
using AMS.Shared.Enums;

namespace AMS.Shared.DTOs;

public class FlatDto
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(20)]
    public string Number { get; set; } = string.Empty;
    
    [Required]
    public FlatType Type { get; set; }
    
    [Required]
    [Range(100, 10000)]
    public decimal Area { get; set; }
    
    [Required]
    [Range(0, 100)]
    public int Floor { get; set; }
    
    [Required]
    public OccupancyStatus Status { get; set; }
    
    public int BlockId { get; set; }
    public string BlockName { get; set; } = string.Empty;
    public string SocietyName { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public List<UserDto> Residents { get; set; } = new();
}

public class CreateFlatDto
{
    [Required]
    [StringLength(20)]
    public string Number { get; set; } = string.Empty;
    
    [Required]
    public FlatType Type { get; set; }
    
    [Required]
    [Range(100, 10000)]
    public decimal Area { get; set; }
    
    [Required]
    [Range(0, 100)]
    public int Floor { get; set; }
    
    [Required]
    public OccupancyStatus Status { get; set; }
    
    [Required]
    public int BlockId { get; set; }
}

public class UpdateFlatDto
{
    [Required]
    [StringLength(20)]
    public string Number { get; set; } = string.Empty;
    
    [Required]
    public FlatType Type { get; set; }
    
    [Required]
    [Range(100, 10000)]
    public decimal Area { get; set; }
    
    [Required]
    [Range(0, 100)]
    public int Floor { get; set; }
    
    [Required]
    public OccupancyStatus Status { get; set; }
}

public class FlatSearchCriteria
{
    public int? SocietyId { get; set; }
    public int? BlockId { get; set; }
    public FlatType? Type { get; set; }
    public OccupancyStatus? Status { get; set; }
    public int? Floor { get; set; }
    public decimal? MinArea { get; set; }
    public decimal? MaxArea { get; set; }
    public string? SearchTerm { get; set; }
}
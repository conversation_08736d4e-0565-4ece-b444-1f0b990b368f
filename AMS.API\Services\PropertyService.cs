using AMS.API.Data;
using AMS.API.Models;
using AMS.Shared.DTOs;
using AMS.Shared.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace AMS.API.Services;

public class PropertyService : IPropertyService
{
    private readonly AmsDbContext _context;

    public PropertyService(AmsDbContext context)
    {
        _context = context;
    }

    #region Society Management

    public async Task<IEnumerable<SocietyDto>> GetAllSocietiesAsync()
    {
        var societies = await _context.Societies
            .Include(s => s.Blocks)
            .ThenInclude(b => b.Flats)
            .ToListAsync();

        return societies.Select(s => new SocietyDto
        {
            Id = s.Id,
            Name = s.Name,
            Address = s.Address,
            ContactEmail = s.ContactEmail,
            ContactPhone = s.ContactPhone,
            CreatedAt = s.CreatedAt,
            UpdatedAt = s.UpdatedAt,
            TotalBlocks = s.Blocks.Count,
            TotalFlats = s.Blocks.SelectMany(b => b.Flats).Count()
        });
    }

    public async Task<SocietyDto?> GetSocietyByIdAsync(int id)
    {
        var society = await _context.Societies
            .Include(s => s.Blocks)
            .ThenInclude(b => b.Flats)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (society == null) return null;

        return new SocietyDto
        {
            Id = society.Id,
            Name = society.Name,
            Address = society.Address,
            ContactEmail = society.ContactEmail,
            ContactPhone = society.ContactPhone,
            CreatedAt = society.CreatedAt,
            UpdatedAt = society.UpdatedAt,
            TotalBlocks = society.Blocks.Count,
            TotalFlats = society.Blocks.SelectMany(b => b.Flats).Count()
        };
    }

    public async Task<SocietyDto> CreateSocietyAsync(CreateSocietyDto dto)
    {
        var society = new Society
        {
            Name = dto.Name,
            Address = dto.Address,
            ContactEmail = dto.ContactEmail,
            ContactPhone = dto.ContactPhone,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Societies.Add(society);
        await _context.SaveChangesAsync();

        return new SocietyDto
        {
            Id = society.Id,
            Name = society.Name,
            Address = society.Address,
            ContactEmail = society.ContactEmail,
            ContactPhone = society.ContactPhone,
            CreatedAt = society.CreatedAt,
            UpdatedAt = society.UpdatedAt,
            TotalBlocks = 0,
            TotalFlats = 0
        };
    }

    public async Task<SocietyDto?> UpdateSocietyAsync(int id, UpdateSocietyDto dto)
    {
        var society = await _context.Societies.FindAsync(id);
        if (society == null) return null;

        society.Name = dto.Name;
        society.Address = dto.Address;
        society.ContactEmail = dto.ContactEmail;
        society.ContactPhone = dto.ContactPhone;
        society.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return await GetSocietyByIdAsync(id);
    }

    public async Task<bool> DeleteSocietyAsync(int id)
    {
        var society = await _context.Societies
            .Include(s => s.Blocks)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (society == null) return false;

        // Check if society has blocks
        if (society.Blocks.Any())
        {
            throw new InvalidOperationException("Cannot delete society with existing blocks");
        }

        _context.Societies.Remove(society);
        await _context.SaveChangesAsync();
        return true;
    }

    #endregion

    #region Block Management

    public async Task<IEnumerable<BlockDto>> GetBlocksBySocietyAsync(int societyId)
    {
        var blocks = await _context.Blocks
            .Include(b => b.Society)
            .Include(b => b.Flats)
            .Where(b => b.SocietyId == societyId)
            .ToListAsync();

        return blocks.Select(b => new BlockDto
        {
            Id = b.Id,
            Name = b.Name,
            NumberOfFloors = b.NumberOfFloors,
            TotalFlats = b.TotalFlats,
            SocietyId = b.SocietyId,
            SocietyName = b.Society.Name,
            CreatedAt = b.CreatedAt,
            UpdatedAt = b.UpdatedAt,
            OccupiedFlats = b.Flats.Count(f => f.Status == Shared.Enums.OccupancyStatus.Occupied),
            VacantFlats = b.Flats.Count(f => f.Status == Shared.Enums.OccupancyStatus.Vacant)
        });
    }

    public async Task<BlockDto?> GetBlockByIdAsync(int id)
    {
        var block = await _context.Blocks
            .Include(b => b.Society)
            .Include(b => b.Flats)
            .FirstOrDefaultAsync(b => b.Id == id);

        if (block == null) return null;

        return new BlockDto
        {
            Id = block.Id,
            Name = block.Name,
            NumberOfFloors = block.NumberOfFloors,
            TotalFlats = block.TotalFlats,
            SocietyId = block.SocietyId,
            SocietyName = block.Society.Name,
            CreatedAt = block.CreatedAt,
            UpdatedAt = block.UpdatedAt,
            OccupiedFlats = block.Flats.Count(f => f.Status == Shared.Enums.OccupancyStatus.Occupied),
            VacantFlats = block.Flats.Count(f => f.Status == Shared.Enums.OccupancyStatus.Vacant)
        };
    }

    public async Task<BlockDto> CreateBlockAsync(CreateBlockDto dto)
    {
        // Verify society exists
        var society = await _context.Societies.FindAsync(dto.SocietyId);
        if (society == null)
        {
            throw new ArgumentException("Society not found");
        }

        var block = new Block
        {
            Name = dto.Name,
            NumberOfFloors = dto.NumberOfFloors,
            TotalFlats = dto.TotalFlats,
            SocietyId = dto.SocietyId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Blocks.Add(block);
        await _context.SaveChangesAsync();

        return new BlockDto
        {
            Id = block.Id,
            Name = block.Name,
            NumberOfFloors = block.NumberOfFloors,
            TotalFlats = block.TotalFlats,
            SocietyId = block.SocietyId,
            SocietyName = society.Name,
            CreatedAt = block.CreatedAt,
            UpdatedAt = block.UpdatedAt,
            OccupiedFlats = 0,
            VacantFlats = 0
        };
    }

    public async Task<BlockDto?> UpdateBlockAsync(int id, UpdateBlockDto dto)
    {
        var block = await _context.Blocks.FindAsync(id);
        if (block == null) return null;

        block.Name = dto.Name;
        block.NumberOfFloors = dto.NumberOfFloors;
        block.TotalFlats = dto.TotalFlats;
        block.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return await GetBlockByIdAsync(id);
    }

    public async Task<bool> DeleteBlockAsync(int id)
    {
        var block = await _context.Blocks
            .Include(b => b.Flats)
            .FirstOrDefaultAsync(b => b.Id == id);

        if (block == null) return false;

        // Check if block has flats
        if (block.Flats.Any())
        {
            throw new InvalidOperationException("Cannot delete block with existing flats");
        }

        _context.Blocks.Remove(block);
        await _context.SaveChangesAsync();
        return true;
    }

    #endregion

    #region Flat Management

    public async Task<IEnumerable<FlatDto>> GetFlatsByBlockAsync(int blockId)
    {
        var flats = await _context.Flats
            .Include(f => f.Block)
            .ThenInclude(b => b.Society)
            .Include(f => f.UserFlats)
            .ThenInclude(uf => uf.User)
            .Where(f => f.BlockId == blockId)
            .ToListAsync();

        return flats.Select(MapFlatToDto);
    }

    public async Task<FlatDto?> GetFlatByIdAsync(int id)
    {
        var flat = await _context.Flats
            .Include(f => f.Block)
            .ThenInclude(b => b.Society)
            .Include(f => f.UserFlats)
            .ThenInclude(uf => uf.User)
            .FirstOrDefaultAsync(f => f.Id == id);

        return flat == null ? null : MapFlatToDto(flat);
    }

    public async Task<FlatDto> CreateFlatAsync(CreateFlatDto dto)
    {
        // Verify block exists
        var block = await _context.Blocks
            .Include(b => b.Society)
            .FirstOrDefaultAsync(b => b.Id == dto.BlockId);

        if (block == null)
        {
            throw new ArgumentException("Block not found");
        }

        var flat = new Flat
        {
            Number = dto.Number,
            Type = dto.Type,
            Area = dto.Area,
            Floor = dto.Floor,
            Status = dto.Status,
            BlockId = dto.BlockId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Flats.Add(flat);
        await _context.SaveChangesAsync();

        return new FlatDto
        {
            Id = flat.Id,
            Number = flat.Number,
            Type = flat.Type,
            Area = flat.Area,
            Floor = flat.Floor,
            Status = flat.Status,
            BlockId = flat.BlockId,
            BlockName = block.Name,
            SocietyName = block.Society.Name,
            CreatedAt = flat.CreatedAt,
            UpdatedAt = flat.UpdatedAt,
            Residents = new List<UserDto>()
        };
    }

    public async Task<FlatDto?> UpdateFlatAsync(int id, UpdateFlatDto dto)
    {
        var flat = await _context.Flats.FindAsync(id);
        if (flat == null) return null;

        flat.Number = dto.Number;
        flat.Type = dto.Type;
        flat.Area = dto.Area;
        flat.Floor = dto.Floor;
        flat.Status = dto.Status;
        flat.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return await GetFlatByIdAsync(id);
    }

    public async Task<bool> DeleteFlatAsync(int id)
    {
        var flat = await _context.Flats
            .Include(f => f.UserFlats)
            .FirstOrDefaultAsync(f => f.Id == id);

        if (flat == null) return false;

        // Remove user associations first
        if (flat.UserFlats.Any())
        {
            _context.UserFlats.RemoveRange(flat.UserFlats);
        }

        _context.Flats.Remove(flat);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<IEnumerable<FlatDto>> SearchFlatsAsync(FlatSearchCriteria criteria)
    {
        var query = _context.Flats
            .Include(f => f.Block)
            .ThenInclude(b => b.Society)
            .Include(f => f.UserFlats)
            .ThenInclude(uf => uf.User)
            .AsQueryable();

        if (criteria.SocietyId.HasValue)
        {
            query = query.Where(f => f.Block.SocietyId == criteria.SocietyId.Value);
        }

        if (criteria.BlockId.HasValue)
        {
            query = query.Where(f => f.BlockId == criteria.BlockId.Value);
        }

        if (criteria.Type.HasValue)
        {
            query = query.Where(f => f.Type == criteria.Type.Value);
        }

        if (criteria.Status.HasValue)
        {
            query = query.Where(f => f.Status == criteria.Status.Value);
        }

        if (criteria.Floor.HasValue)
        {
            query = query.Where(f => f.Floor == criteria.Floor.Value);
        }

        if (criteria.MinArea.HasValue)
        {
            query = query.Where(f => f.Area >= criteria.MinArea.Value);
        }

        if (criteria.MaxArea.HasValue)
        {
            query = query.Where(f => f.Area <= criteria.MaxArea.Value);
        }

        if (!string.IsNullOrEmpty(criteria.SearchTerm))
        {
            query = query.Where(f => f.Number.Contains(criteria.SearchTerm) ||
                                   f.Block.Name.Contains(criteria.SearchTerm) ||
                                   f.Block.Society.Name.Contains(criteria.SearchTerm));
        }

        var flats = await query.ToListAsync();
        return flats.Select(MapFlatToDto);
    }

    #endregion

    #region Private Helper Methods

    private static FlatDto MapFlatToDto(Flat flat)
    {
        return new FlatDto
        {
            Id = flat.Id,
            Number = flat.Number,
            Type = flat.Type,
            Area = flat.Area,
            Floor = flat.Floor,
            Status = flat.Status,
            BlockId = flat.BlockId,
            BlockName = flat.Block.Name,
            SocietyName = flat.Block.Society.Name,
            CreatedAt = flat.CreatedAt,
            UpdatedAt = flat.UpdatedAt,
            Residents = flat.UserFlats.Where(uf => uf.EndDate == null).Select(uf => new UserDto
            {
                Id = uf.User.Id,
                Name = uf.User.Name,
                Email = uf.User.Email,
                Phone = uf.User.Phone,
                Role = uf.User.Role,
                IsActive = uf.User.IsActive,
                CreatedAt = uf.User.CreatedAt,
                UpdatedAt = uf.User.UpdatedAt
            }).ToList()
        };
    }

    #endregion
}
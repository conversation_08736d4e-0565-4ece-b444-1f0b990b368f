using AMS.API.Models;
using Microsoft.EntityFrameworkCore;

namespace AMS.API.Data;

public class AmsDbContext : DbContext
{
    public AmsDbContext(DbContextOptions<AmsDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Society> Societies { get; set; }
    public DbSet<Block> Blocks { get; set; }
    public DbSet<Flat> Flats { get; set; }
    public DbSet<UserFlat> UserFlats { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure Society entity
        modelBuilder.Entity<Society>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Address).IsRequired().HasMaxLength(500);
            entity.Property(e => e.ContactEmail).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ContactPhone).IsRequired().HasMaxLength(20);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            
            entity.HasIndex(e => e.Name).IsUnique();
            entity.HasIndex(e => e.ContactEmail).IsUnique();
        });

        // Configure Block entity
        modelBuilder.Entity<Block>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
            entity.Property(e => e.NumberOfFloors).IsRequired();
            entity.Property(e => e.TotalFlats).IsRequired();
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");

            entity.HasOne(e => e.Society)
                  .WithMany(s => s.Blocks)
                  .HasForeignKey(e => e.SocietyId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => new { e.SocietyId, e.Name }).IsUnique();
        });

        // Configure Flat entity
        modelBuilder.Entity<Flat>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Number).IsRequired().HasMaxLength(20);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Area).IsRequired().HasColumnType("decimal(8,2)");
            entity.Property(e => e.Floor).IsRequired();
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");

            entity.HasOne(e => e.Block)
                  .WithMany(b => b.Flats)
                  .HasForeignKey(e => e.BlockId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => new { e.BlockId, e.Number }).IsUnique();
        });

        // Configure User entity
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
            entity.Property(e => e.PasswordHash).IsRequired();
            entity.Property(e => e.Phone).HasMaxLength(20);
            entity.Property(e => e.Role).IsRequired();
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");

            entity.HasIndex(e => e.Email).IsUnique();
        });

        // Configure UserFlat entity (Many-to-Many relationship)
        modelBuilder.Entity<UserFlat>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.IsPrimaryContact).HasDefaultValue(false);
            entity.Property(e => e.StartDate).HasDefaultValueSql("GETUTCDATE()");

            entity.HasOne(e => e.User)
                  .WithMany(u => u.UserFlats)
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Flat)
                  .WithMany(f => f.UserFlats)
                  .HasForeignKey(e => e.FlatId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.UserId, e.FlatId, e.StartDate });
        });

        // Configure User-Society relationship for managers
        modelBuilder.Entity<User>()
            .HasMany(u => u.ManagedSocieties)
            .WithMany(s => s.SocietyManagers)
            .UsingEntity<Dictionary<string, object>>(
                "UserSociety",
                j => j.HasOne<Society>().WithMany().HasForeignKey("SocietyId"),
                j => j.HasOne<User>().WithMany().HasForeignKey("UserId"),
                j =>
                {
                    j.HasKey("UserId", "SocietyId");
                    j.ToTable("UserSocieties");
                });
    }
}
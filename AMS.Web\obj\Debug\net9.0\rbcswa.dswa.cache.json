{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["ZWvLvVlsarooZzd5Dki2j+nrX0w0LIoXGSNiAUR0nc0=", "/fooGjUsKsmjNWTu3/+AdOgrfHQ3mMsPOGQRWT4AqE0=", "dJw154ncKDEhWh9hgyOIvI5jSKFxbAAUS2V1yZ7kJu8=", "4wrsA/J9qj8B0rWYZ8mSn6hif0lIXgXKttQCzVOIx0g=", "mmj7nWWITyTE7xhNNYqJAN5l/7DdFlwjYJU5ppq4G8w=", "zdTJkYH7bsMtHYZRtV9oJbRN1puyNFLENKBBeMEJQxk=", "Ff24Zs3ophKsiEiJ4dAIyk+/3SMCy1NyK5NWux1pz5A=", "HAHIMtl6QdSeCe8cQPcTHZalyCJnUPGRGk0jXVOh+mg=", "cXiCaibYmr+pu2nRAycMbDDQc6M7W86isl4UaZLv2R0=", "ZzJbOMV4oofMPGFvHvTlK2fWMaukotruRN5yaUtvtgA=", "+ebd/CrOm9nG5R+MvrqQF8k3Aa0tyN+mBzF0htp+Zt0=", "CVkOhvrjoSxCi1sBl41doPBpCBcivmg3m3mIGvHW+3g=", "R6sIXzS7TqT48BuSoTQQBfs348vI6eDb8gXAlaYupNw=", "HExR+bKBBceAlM51rZ/GK2b0zpdZXwgDIYeYlMROIHE=", "mfsQPWRV3GJxDe+QFnK3eEOA3lP9+hvvBA13yoBPSIo=", "oiagi22/xcKoQnaDsCt2QyAuMBGgkTYRG6CzDhYgX8g=", "BTJn1I9Bt2GtajDq0/gWXl5N6lwqpjzHEOLZU59c44A=", "xD1S4U6NkVQRYmepUIXEsOafTrJCXKRRTpnktxDL8dY=", "k5XXULr9JpkuZqLYeUEQvpOSXOZ/R3FJVOc7MtakgUI=", "Jmz0r+ialAVfyjI056yt8anqUSE3+V+9mN9Wp0XBH4A=", "MW6Vu2rgV1FVcA3vY2vBc//Y1Q2SQcOZBpOuJ7541gM=", "4FwPIhM1+pTv6LD47A/gp9wS3U+voRVxweRgCk6n34s=", "REh17KkvsmD4K2HcScLX1xLZPXFhf9XMV/0bh7zbi3w=", "sIlZ+uTG4fhzybIITvtLNzL3b9sLLDe/0JPOtU5Kn30=", "XPsipFBuBJBISMsvAuKdOJVFXmczgU/vk2CD7PK+P7w=", "91x+Nk+cpYdgPuwGAbQpyKwFAXF7/3cmanVATeGxjB8=", "5S43Zm5g1AScEwMMnWQh/BKGRYrmBODe8ruz1LhINoE=", "mBTX9W5mWmJdtpONFzVign1zOroec3tdz9NEMJ4IdiU=", "8m//THmXBCLnIED+0IqBEMzYVdBNp/2MDP6KArJjnH0=", "kNoAoki986oNe8kO9wl4EZG8qVic5+/8+NHL0vWWpZI=", "MoLCprno7dBSUJdAaEla8pq2Oz+BgWnw96GbC/IJ590=", "Q1QOZfEpJSeApT5Gz5kwQxyB6r+OlfL8crV60GUDkEY=", "tw2oB+iIzGtQjS+dX9YzH90j3Oda+MZwcS4FuKHgnQM=", "zBvy4L/kUhJAR7NtdvYtZbBER9vZq4T7yaMqqJZgMUs=", "cSnIS1SjLJEFsCe3BvUl5PqATUokkTBwzgAzPzxOmb4=", "jayroztRKbcvErU5In6iiQeVClcOU+EeIyYwD7Q6IbA=", "KdI8qxEUAUAPklVcCqmDUpnZqBHYiEHrgfv/VH3eOLA=", "uNSGivV5YmGe6r5B+7U/HpvyAs6+WuIDrYwvHpk51h8=", "wYu1TOxGndGpP86sFDQDtkkVBAPx331hh3noouErKoM=", "xBnAffUZ9FSxXXhk0s4PVLYbuqoDHzkEuMBpIJvfjaM=", "pHn64Zii2vpmvxs2+INmmedP4HKQA+3px3jd5R/TlAM=", "xb7q7qifz9hGsF+kwuSTxQ0V/7p3p3RYSs03BY0yMDE=", "nLLydRaBI1Ib8HQJMGTNN5k52mEW+bPnFE0J9fgnpiw=", "vErgh6m+TWsOp8SfQ652Hab9io1kQ3j+W8QiKgriu7I=", "SdArkRgTQXd/hwV7k9yOv/103gyPFlnSKjl0h7n3mJA=", "i0lHEhg4lEkB0b9FFdLcv69wB5zliZAwLhQY+nU8yVg=", "RLQLYxdMcOCU6dVQKfp69EWmIOPNRam7J2fqmVs0h1A=", "Pgl0LLjp04vuKv4ekjWqRyizXw1x66kHhoBlQ2h9Vo4=", "xCypCF8Smkp/p0NNoI+/g7IQ93OnkTS9zDMSaQJjLD0=", "KWvM9lGCORM4rtWazZwjoHiUMZPtgojCBxEXTsbseEQ=", "I9I+9CjF3ZLt6VlYDGVViP1myiFyxS0GryPL6RfBNzw=", "3uagJhd0QNifSLFyZAoQABPKeuLuRCNCGWelS5zjznI=", "7sG29z5DiX6zGrFsheFTUNow/UUTwkE58hZ8CVKv5FE=", "DbkPVTUeSo0EEbkmcYDgtJIF+kpKmx5zUeREKPfIerY=", "XIZ2mrlGXNUYIM0IVUva9kFCn5jAJePcboRnhUHhdnQ=", "0Ag7X+LPW4iRUD+wpAbcdfZStm/zN0WfMlVkOlAZagU=", "IMxfUjH3AnnLG71GWhs2ivrDt64M3WEE9Q3GbaNJNiY=", "asMDn4/w4UPpclz31CrJnm2xDdZkTKb2VLc593fDJrs=", "iWawrLrHi71UJ1aSjlbl5HAcnpr/EKPR33IFQDeGreY=", "zKLqSusPJH2+dTj+AdhneOBvi21DCBOSvcOz1pnbETE=", "Ri9AOkgJx4EjAyBD4ZV/4F1r9hoVS4/92MyGYrge09g=", "Vwy09QtMpZ5q9Oit6p35ReF0wRe1V34esCECuSju3ak=", "BEv3fwQVb/KvhWlNwPD330wEXcGH84M66mfo2KjBP2Y=", "v54fBH4tdcUmSMKrt+VMLwFSWc8sZ1C9S1sj3ogOMKM="], "CachedAssets": {"ZWvLvVlsarooZzd5Dki2j+nrX0w0LIoXGSNiAUR0nc0=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\21ozekq88a-b9sayid5wm.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-07-17T10:16:30.5118049+00:00"}, "/fooGjUsKsmjNWTu3/+AdOgrfHQ3mMsPOGQRWT4AqE0=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\hdw9adylm2-61n19gt1b8.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-07-17T10:16:30.5179125+00:00"}, "dJw154ncKDEhWh9hgyOIvI5jSKFxbAAUS2V1yZ7kJu8=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\bi45vf2vuj-xtxxf3hu2r.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-07-17T10:16:30.5142239+00:00"}, "4wrsA/J9qj8B0rWYZ8mSn6hif0lIXgXKttQCzVOIx0g=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\35ixsirlml-bqjiyaj88i.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-17T10:16:30.5179125+00:00"}, "mmj7nWWITyTE7xhNNYqJAN5l/7DdFlwjYJU5ppq4G8w=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\p7vklz0o3b-c2jlpeoesf.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "zdTJkYH7bsMtHYZRtV9oJbRN1puyNFLENKBBeMEJQxk=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\jppl6dz6nu-erw9l3u2r3.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-17T10:16:30.5210447+00:00"}, "Ff24Zs3ophKsiEiJ4dAIyk+/3SMCy1NyK5NWux1pz5A=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\mar920mhti-aexeepp0ev.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-17T10:16:30.5179125+00:00"}, "HAHIMtl6QdSeCe8cQPcTHZalyCJnUPGRGk0jXVOh+mg=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\c7xcgww1p1-d7shbmvgxk.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-17T10:16:30.521877+00:00"}, "cXiCaibYmr+pu2nRAycMbDDQc6M7W86isl4UaZLv2R0=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\4guck9uwb7-ausgxo2sd3.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-17T10:16:30.518923+00:00"}, "ZzJbOMV4oofMPGFvHvTlK2fWMaukotruRN5yaUtvtgA=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\m00vt0i3zk-k8d9w2qqmf.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-17T10:16:30.521877+00:00"}, "+ebd/CrOm9nG5R+MvrqQF8k3Aa0tyN+mBzF0htp+Zt0=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\cyk9kl5g7e-cosvhxvwiu.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "CVkOhvrjoSxCi1sBl41doPBpCBcivmg3m3mIGvHW+3g=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\1nf1f20iyr-ub07r2b239.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-17T10:16:30.518923+00:00"}, "R6sIXzS7TqT48BuSoTQQBfs348vI6eDb8gXAlaYupNw=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\63shoem4wu-fvhpjtyr6v.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-17T10:16:30.518923+00:00"}, "HExR+bKBBceAlM51rZ/GK2b0zpdZXwgDIYeYlMROIHE=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\53odtmow9i-b7pk76d08c.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-17T10:16:30.521877+00:00"}, "mfsQPWRV3GJxDe+QFnK3eEOA3lP9+hvvBA13yoBPSIo=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\vyldnbnety-fsbi9cje9m.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "oiagi22/xcKoQnaDsCt2QyAuMBGgkTYRG6CzDhYgX8g=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\p629m0wuku-rzd6atqjts.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-17T10:16:30.5202763+00:00"}, "BTJn1I9Bt2GtajDq0/gWXl5N6lwqpjzHEOLZU59c44A=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\x4295c0ydk-ee0r1s7dh0.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "xD1S4U6NkVQRYmepUIXEsOafTrJCXKRRTpnktxDL8dY=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\5l41mx8vee-dxx9fxp4il.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-17T10:16:30.5202763+00:00"}, "k5XXULr9JpkuZqLYeUEQvpOSXOZ/R3FJVOc7MtakgUI=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\snavv31mic-jd9uben2k1.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "Jmz0r+ialAVfyjI056yt8anqUSE3+V+9mN9Wp0XBH4A=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\w7yrwcxchj-khv3u5hwcm.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-17T10:16:30.5202763+00:00"}, "MW6Vu2rgV1FVcA3vY2vBc//Y1Q2SQcOZBpOuJ7541gM=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\9tw0jzyzcl-r4e9w2rdcm.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-17T10:16:30.5199217+00:00"}, "4FwPIhM1+pTv6LD47A/gp9wS3U+voRVxweRgCk6n34s=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\k0indrb3vj-lcd1t2u6c8.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-17T10:16:30.5224988+00:00"}, "REh17KkvsmD4K2HcScLX1xLZPXFhf9XMV/0bh7zbi3w=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\79oorl7gjs-c2oey78nd0.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "sIlZ+uTG4fhzybIITvtLNzL3b9sLLDe/0JPOtU5Kn30=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\6m3o40mytz-tdbxkamptv.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-17T10:16:30.5202763+00:00"}, "XPsipFBuBJBISMsvAuKdOJVFXmczgU/vk2CD7PK+P7w=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\xkm03r6j3e-j5mq2jizvt.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-17T10:16:30.5179125+00:00"}, "91x+Nk+cpYdgPuwGAbQpyKwFAXF7/3cmanVATeGxjB8=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\cid9bpyn3e-06098lyss8.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-17T10:16:30.5224988+00:00"}, "5S43Zm5g1AScEwMMnWQh/BKGRYrmBODe8ruz1LhINoE=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\00l8tglkct-nvvlpmu67g.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-17T10:16:30.5179125+00:00"}, "mBTX9W5mWmJdtpONFzVign1zOroec3tdz9NEMJ4IdiU=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\jynvd7cbhw-s35ty4nyc5.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "8m//THmXBCLnIED+0IqBEMzYVdBNp/2MDP6KArJjnH0=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\neojyym3tg-pj5nd1wqec.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-17T10:16:30.5255547+00:00"}, "kNoAoki986oNe8kO9wl4EZG8qVic5+/8+NHL0vWWpZI=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\0ic5gqdvwh-46ein0sx1k.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-17T10:16:30.5255547+00:00"}, "MoLCprno7dBSUJdAaEla8pq2Oz+BgWnw96GbC/IJ590=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\ij07g9qsrg-v0zj4ognzu.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-17T10:16:30.5235577+00:00"}, "Q1QOZfEpJSeApT5Gz5kwQxyB6r+OlfL8crV60GUDkEY=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\6k9kulh3zb-37tfw0ft22.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-17T10:16:30.5275556+00:00"}, "tw2oB+iIzGtQjS+dX9YzH90j3Oda+MZwcS4FuKHgnQM=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\1nbrmjc9bi-hrwsygsryq.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-17T10:16:30.5304414+00:00"}, "zBvy4L/kUhJAR7NtdvYtZbBER9vZq4T7yaMqqJZgMUs=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\nzxx601ynu-pk9g2wxc8p.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-17T10:16:30.5255547+00:00"}, "cSnIS1SjLJEFsCe3BvUl5PqATUokkTBwzgAzPzxOmb4=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\4qntagyul1-ft3s53vfgj.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-17T10:16:30.5255547+00:00"}, "jayroztRKbcvErU5In6iiQeVClcOU+EeIyYwD7Q6IbA=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\d0lb6f5xdo-6cfz1n2cew.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-17T10:16:30.5265545+00:00"}, "KdI8qxEUAUAPklVcCqmDUpnZqBHYiEHrgfv/VH3eOLA=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\iqqd9olhww-6pdc2jztkx.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-17T10:16:30.5255547+00:00"}, "uNSGivV5YmGe6r5B+7U/HpvyAs6+WuIDrYwvHpk51h8=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\gm0ip5om15-493y06b0oq.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "wYu1TOxGndGpP86sFDQDtkkVBAPx331hh3noouErKoM=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\sdl5v69afb-iovd86k7lj.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-17T10:16:30.518923+00:00"}, "xBnAffUZ9FSxXXhk0s4PVLYbuqoDHzkEuMBpIJvfjaM=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\nlbyqax0au-vr1egmr9el.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "pHn64Zii2vpmvxs2+INmmedP4HKQA+3px3jd5R/TlAM=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\w042nrb9q9-kbrnm935zg.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-17T10:16:30.5215727+00:00"}, "xb7q7qifz9hGsF+kwuSTxQ0V/7p3p3RYSs03BY0yMDE=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\37y06tkvla-jj8uyg4cgr.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "nLLydRaBI1Ib8HQJMGTNN5k52mEW+bPnFE0J9fgnpiw=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\gyu9sg40of-y7v9cxd14o.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-17T10:16:30.5162312+00:00"}, "vErgh6m+TWsOp8SfQ652Hab9io1kQ3j+W8QiKgriu7I=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\a602kcpe1s-notf2xhcfb.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "SdArkRgTQXd/hwV7k9yOv/103gyPFlnSKjl0h7n3mJA=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\0kmwborjml-h1s4sie4z3.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-17T10:16:30.518923+00:00"}, "i0lHEhg4lEkB0b9FFdLcv69wB5zliZAwLhQY+nU8yVg=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\jaa99fw14s-63fj8s7r0e.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "RLQLYxdMcOCU6dVQKfp69EWmIOPNRam7J2fqmVs0h1A=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\f3bsdu4lax-0j3bgjxly4.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-17T10:16:30.5275556+00:00"}, "Pgl0LLjp04vuKv4ekjWqRyizXw1x66kHhoBlQ2h9Vo4=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\ye26a055mk-47otxtyo56.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-07-17T10:16:30.5284094+00:00"}, "xCypCF8Smkp/p0NNoI+/g7IQ93OnkTS9zDMSaQJjLD0=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\xvqp0ob4gs-4v8eqarkd7.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "KWvM9lGCORM4rtWazZwjoHiUMZPtgojCBxEXTsbseEQ=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\wcyix71b30-356vix0kms.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-07-17T10:16:30.5275556+00:00"}, "I9I+9CjF3ZLt6VlYDGVViP1myiFyxS0GryPL6RfBNzw=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\0xg8wergnj-83jwlth58m.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "3uagJhd0QNifSLFyZAoQABPKeuLuRCNCGWelS5zjznI=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\puf6kx29ab-mrlpezrjn3.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-07-17T10:16:30.5275556+00:00"}, "7sG29z5DiX6zGrFsheFTUNow/UUTwkE58hZ8CVKv5FE=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\sggaejkcvs-lzl9nlhx6b.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-07-17T10:16:30.5245547+00:00"}, "DbkPVTUeSo0EEbkmcYDgtJIF+kpKmx5zUeREKPfIerY=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\hrwfm6858q-ag7o75518u.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-07-17T10:16:30.5275556+00:00"}, "XIZ2mrlGXNUYIM0IVUva9kFCn5jAJePcboRnhUHhdnQ=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\6xhw0e7oqs-x0q3zqp4vz.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-07-17T10:16:30.5235577+00:00"}, "0Ag7X+LPW4iRUD+wpAbcdfZStm/zN0WfMlVkOlAZagU=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\o5mzqfv6u0-0i3buxo5is.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-07-17T10:16:30.5304414+00:00"}, "IMxfUjH3AnnLG71GWhs2ivrDt64M3WEE9Q3GbaNJNiY=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\h3kve2fifr-o1o13a6vjx.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-07-17T10:16:30.5265545+00:00"}, "asMDn4/w4UPpclz31CrJnm2xDdZkTKb2VLc593fDJrs=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\6p6wqi6oyi-ttgo8qnofa.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-07-17T10:16:30.5304414+00:00"}, "iWawrLrHi71UJ1aSjlbl5HAcnpr/EKPR33IFQDeGreY=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\brzcsnfnvl-2z0ns9nrw6.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-07-17T10:16:30.5284094+00:00"}, "zKLqSusPJH2+dTj+AdhneOBvi21DCBOSvcOz1pnbETE=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\agqugxb009-muycvpuwrr.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-07-17T10:16:30.5304414+00:00"}, "Ri9AOkgJx4EjAyBD4ZV/4F1r9hoVS4/92MyGYrge09g=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\c8fqjp8uup-87fc7y1x7t.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-07-17T10:16:30.5284094+00:00"}, "Vwy09QtMpZ5q9Oit6p35ReF0wRe1V34esCECuSju3ak=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\pj58dswna9-mlv21k5csn.gz", "SourceId": "AMS.Web", "SourceType": "Discovered", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-07-17T10:16:30.5284094+00:00"}, "BEv3fwQVb/KvhWlNwPD330wEXcGH84M66mfo2KjBP2Y=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\x3wf8eqw26-77bvolffif.gz", "SourceId": "AMS.Web", "SourceType": "Computed", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "AMS.Web#[.{fingerprint=77bvolffif}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\AMS.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2xa1bzwsp8", "Integrity": "ertWE9INZxCq4La6+eExMjU1ddToucrULBDQC2HZwU0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\AMS.Web.styles.css", "FileLength": 540, "LastWriteTime": "2025-07-17T10:16:30.5255547+00:00"}, "v54fBH4tdcUmSMKrt+VMLwFSWc8sZ1C9S1sj3ogOMKM=": {"Identity": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\vd0rb3tjmf-77bvolffif.gz", "SourceId": "AMS.Web", "SourceType": "Computed", "ContentRoot": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/AMS.Web", "RelativePath": "AMS.Web#[.{fingerprint=77bvolffif}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\AMS.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2xa1bzwsp8", "Integrity": "ertWE9INZxCq4La6+eExMjU1ddToucrULBDQC2HZwU0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "F:\\RMS\\AMS\\AMS.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\AMS.Web.bundle.scp.css", "FileLength": 540, "LastWriteTime": "2025-07-17T10:16:30.5265545+00:00"}}, "CachedCopyCandidates": {}}
using System.ComponentModel.DataAnnotations;

namespace AMS.Shared.DTOs;

public class SocietyDto
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string ContactEmail { get; set; } = string.Empty;
    
    [Required]
    [Phone]
    [StringLength(20)]
    public string ContactPhone { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public int TotalBlocks { get; set; }
    public int TotalFlats { get; set; }
}

public class CreateSocietyDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string ContactEmail { get; set; } = string.Empty;
    
    [Required]
    [Phone]
    [StringLength(20)]
    public string ContactPhone { get; set; } = string.Empty;
}

public class UpdateSocietyDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string ContactEmail { get; set; } = string.Empty;
    
    [Required]
    [Phone]
    [StringLength(20)]
    public string ContactPhone { get; set; } = string.Empty;
}
using System.ComponentModel.DataAnnotations;

namespace AMS.API.Models;

public class Society
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string Address { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string ContactEmail { get; set; } = string.Empty;

    [Required]
    [Phone]
    [StringLength(20)]
    public string ContactPhone { get; set; } = string.Empty;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation Properties
    public ICollection<Block> Blocks { get; set; } = new List<Block>();
    public ICollection<User> SocietyManagers { get; set; } = new List<User>();
}
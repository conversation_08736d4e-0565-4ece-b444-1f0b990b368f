# Design Document

## Overview

The Core Property Management feature will extend the existing ASP.NET Core 9.0 architecture to support hierarchical property management (Society → Block → Flat) with role-based access control. The design leverages the existing three-project structure (API, Web, Shared) and builds upon the current Entity Framework Core setup with SQL Server.

The system will implement a layered architecture pattern with clear separation of concerns:
- **API Layer**: RESTful controllers and data access
- **Web Layer**: MVC views and user interface
- **Shared Layer**: DTOs, interfaces, and common utilities

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Web MVC Controllers] --> B[Razor Views]
        C[API Controllers] --> D[HTTP Responses]
    end
    
    subgraph "Business Logic Layer"
        E[Property Services] --> F[User Services]
        G[Authorization Services] --> H[Validation Services]
    end
    
    subgraph "Data Access Layer"
        I[Entity Framework DbContext] --> J[Repository Pattern]
        K[Data Models] --> L[SQL Server Database]
    end
    
    A --> E
    C --> E
    E --> I
    F --> I
    G --> I
```

### Project Structure Enhancement

The design will extend the existing project structure:

```
AMS.API/
├── Controllers/
│   ├── SocietiesController.cs
│   ├── BlocksController.cs
│   ├── FlatsController.cs
│   └── UsersController.cs (enhanced)
├── Data/
│   └── AmsDbContext.cs (enhanced)
├── Models/
│   ├── User.cs (enhanced)
│   ├── Society.cs
│   ├── Block.cs
│   └── Flat.cs
└── Services/
    ├── IPropertyService.cs
    ├── PropertyService.cs
    ├── IUserService.cs
    └── UserService.cs

AMS.Web/
├── Controllers/
│   ├── SocietiesController.cs
│   ├── BlocksController.cs
│   ├── FlatsController.cs
│   └── UsersController.cs
├── Views/
│   ├── Societies/
│   ├── Blocks/
│   ├── Flats/
│   └── Users/
└── Models/
    └── ViewModels/

AMS.Shared/
├── DTOs/
│   ├── SocietyDto.cs
│   ├── BlockDto.cs
│   ├── FlatDto.cs
│   └── UserDto.cs
├── Enums/
│   ├── UserRole.cs
│   ├── FlatType.cs
│   └── OccupancyStatus.cs
└── Interfaces/
    ├── IPropertyService.cs
    └── IUserService.cs
```

## Components and Interfaces

### Core Data Models

#### Society Model
```csharp
public class Society
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public string ContactEmail { get; set; }
    public string ContactPhone { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation Properties
    public ICollection<Block> Blocks { get; set; }
    public ICollection<User> SocietyManagers { get; set; }
}
```

#### Block Model
```csharp
public class Block
{
    public int Id { get; set; }
    public string Name { get; set; }
    public int NumberOfFloors { get; set; }
    public int TotalFlats { get; set; }
    public int SocietyId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation Properties
    public Society Society { get; set; }
    public ICollection<Flat> Flats { get; set; }
}
```

#### Flat Model
```csharp
public class Flat
{
    public int Id { get; set; }
    public string Number { get; set; }
    public FlatType Type { get; set; }
    public decimal Area { get; set; }
    public int Floor { get; set; }
    public OccupancyStatus Status { get; set; }
    public int BlockId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation Properties
    public Block Block { get; set; }
    public ICollection<UserFlat> UserFlats { get; set; }
}
```

#### Enhanced User Model
```csharp
public class User
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public string PasswordHash { get; set; }
    public string Phone { get; set; }
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    // Navigation Properties
    public ICollection<UserFlat> UserFlats { get; set; }
    public ICollection<Society> ManagedSocieties { get; set; }
}
```

#### UserFlat Association Model
```csharp
public class UserFlat
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int FlatId { get; set; }
    public ResidentType Type { get; set; } // Owner, Tenant, Family
    public bool IsPrimaryContact { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    
    // Navigation Properties
    public User User { get; set; }
    public Flat Flat { get; set; }
}
```

### Service Interfaces

#### IPropertyService
```csharp
public interface IPropertyService
{
    // Society Management
    Task<IEnumerable<SocietyDto>> GetAllSocietiesAsync();
    Task<SocietyDto> GetSocietyByIdAsync(int id);
    Task<SocietyDto> CreateSocietyAsync(CreateSocietyDto dto);
    Task<SocietyDto> UpdateSocietyAsync(int id, UpdateSocietyDto dto);
    Task<bool> DeleteSocietyAsync(int id);
    
    // Block Management
    Task<IEnumerable<BlockDto>> GetBlocksBySocietyAsync(int societyId);
    Task<BlockDto> GetBlockByIdAsync(int id);
    Task<BlockDto> CreateBlockAsync(CreateBlockDto dto);
    Task<BlockDto> UpdateBlockAsync(int id, UpdateBlockDto dto);
    Task<bool> DeleteBlockAsync(int id);
    
    // Flat Management
    Task<IEnumerable<FlatDto>> GetFlatsByBlockAsync(int blockId);
    Task<FlatDto> GetFlatByIdAsync(int id);
    Task<FlatDto> CreateFlatAsync(CreateFlatDto dto);
    Task<FlatDto> UpdateFlatAsync(int id, UpdateFlatDto dto);
    Task<bool> DeleteFlatAsync(int id);
    Task<IEnumerable<FlatDto>> SearchFlatsAsync(FlatSearchCriteria criteria);
}
```

#### IUserService
```csharp
public interface IUserService
{
    Task<UserDto> CreateUserAsync(CreateUserDto dto);
    Task<UserDto> GetUserByIdAsync(int id);
    Task<UserDto> GetUserByEmailAsync(string email);
    Task<UserDto> UpdateUserAsync(int id, UpdateUserDto dto);
    Task<bool> AssignUserToFlatAsync(int userId, int flatId, ResidentType type);
    Task<bool> RemoveUserFromFlatAsync(int userId, int flatId);
    Task<IEnumerable<UserDto>> GetFlatResidentsAsync(int flatId);
    Task<bool> AuthenticateAsync(string email, string password);
}
```

### API Controllers

#### SocietiesController
- GET /api/societies - List all societies
- GET /api/societies/{id} - Get society details
- POST /api/societies - Create new society
- PUT /api/societies/{id} - Update society
- DELETE /api/societies/{id} - Delete society

#### BlocksController
- GET /api/societies/{societyId}/blocks - List blocks in society
- GET /api/blocks/{id} - Get block details
- POST /api/blocks - Create new block
- PUT /api/blocks/{id} - Update block
- DELETE /api/blocks/{id} - Delete block

#### FlatsController
- GET /api/blocks/{blockId}/flats - List flats in block
- GET /api/flats/{id} - Get flat details
- GET /api/flats/search - Search flats with criteria
- POST /api/flats - Create new flat
- PUT /api/flats/{id} - Update flat
- DELETE /api/flats/{id} - Delete flat

## Data Models

### Database Schema Design

```mermaid
erDiagram
    Society ||--o{ Block : contains
    Block ||--o{ Flat : contains
    User ||--o{ UserFlat : has
    Flat ||--o{ UserFlat : occupied_by
    User ||--o{ Society : manages
    
    Society {
        int Id PK
        string Name
        string Address
        string ContactEmail
        string ContactPhone
        datetime CreatedAt
        datetime UpdatedAt
    }
    
    Block {
        int Id PK
        string Name
        int NumberOfFloors
        int TotalFlats
        int SocietyId FK
        datetime CreatedAt
        datetime UpdatedAt
    }
    
    Flat {
        int Id PK
        string Number
        enum FlatType
        decimal Area
        int Floor
        enum OccupancyStatus
        int BlockId FK
        datetime CreatedAt
        datetime UpdatedAt
    }
    
    User {
        int Id PK
        string Name
        string Email
        string PasswordHash
        string Phone
        enum UserRole
        bool IsActive
        datetime CreatedAt
        datetime UpdatedAt
    }
    
    UserFlat {
        int Id PK
        int UserId FK
        int FlatId FK
        enum ResidentType
        bool IsPrimaryContact
        datetime StartDate
        datetime EndDate
    }
```

### Enumerations

```csharp
public enum UserRole
{
    SuperAdmin = 1,
    SocietyManager = 2,
    Resident = 3,
    Staff = 4
}

public enum FlatType
{
    OneBHK = 1,
    TwoBHK = 2,
    ThreeBHK = 3,
    FourBHK = 4,
    Penthouse = 5,
    Studio = 6
}

public enum OccupancyStatus
{
    Vacant = 1,
    Occupied = 2,
    UnderMaintenance = 3
}

public enum ResidentType
{
    Owner = 1,
    Tenant = 2,
    Family = 3
}
```

## Error Handling

### Exception Handling Strategy

1. **Global Exception Handler**: Implement middleware to catch and handle unhandled exceptions
2. **Custom Exceptions**: Create domain-specific exceptions for business logic violations
3. **Validation Errors**: Use Data Annotations and FluentValidation for input validation
4. **API Error Responses**: Standardized error response format

```csharp
public class ApiErrorResponse
{
    public string Message { get; set; }
    public string Details { get; set; }
    public int StatusCode { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, string[]> ValidationErrors { get; set; }
}
```

### Custom Exceptions

```csharp
public class PropertyNotFoundException : Exception
public class InvalidPropertyOperationException : Exception
public class UnauthorizedAccessException : Exception
public class ValidationException : Exception
```

## Testing Strategy

### Unit Testing
- **Service Layer Tests**: Test business logic in isolation using mocking
- **Controller Tests**: Test API endpoints with mock services
- **Model Validation Tests**: Test data annotations and validation rules

### Integration Testing
- **Database Integration**: Test Entity Framework operations against test database
- **API Integration**: Test complete request/response cycles
- **Authentication/Authorization**: Test role-based access control

### Test Structure
```
AMS.Tests/
├── Unit/
│   ├── Services/
│   ├── Controllers/
│   └── Models/
├── Integration/
│   ├── API/
│   └── Database/
└── TestUtilities/
    ├── Fixtures/
    └── Helpers/
```

### Testing Tools
- **xUnit**: Primary testing framework
- **Moq**: Mocking framework for dependencies
- **Microsoft.AspNetCore.Mvc.Testing**: For integration testing
- **Microsoft.EntityFrameworkCore.InMemory**: For database testing

The design ensures scalability, maintainability, and follows established .NET Core patterns while building upon the existing codebase structure.
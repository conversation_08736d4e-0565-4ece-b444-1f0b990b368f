using System.ComponentModel.DataAnnotations;
using AMS.Shared.Enums;

namespace AMS.Shared.DTOs;

public class UserDto
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [Phone]
    [StringLength(20)]
    public string Phone { get; set; } = string.Empty;
    
    [Required]
    public UserRole Role { get; set; }
    
    public bool IsActive { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public List<UserFlatDto> AssignedFlats { get; set; } = new();
}

public class CreateUserDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = string.Empty;
    
    [Phone]
    [StringLength(20)]
    public string Phone { get; set; } = string.Empty;
    
    [Required]
    public UserRole Role { get; set; }
}

public class UpdateUserDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;
    
    [Phone]
    [StringLength(20)]
    public string Phone { get; set; } = string.Empty;
    
    [Required]
    public UserRole Role { get; set; }
    
    public bool IsActive { get; set; }
}

public class UserFlatDto
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int FlatId { get; set; }
    public string FlatNumber { get; set; } = string.Empty;
    public string BlockName { get; set; } = string.Empty;
    public string SocietyName { get; set; } = string.Empty;
    public ResidentType Type { get; set; }
    public bool IsPrimaryContact { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class AssignUserToFlatDto
{
    [Required]
    public int UserId { get; set; }
    
    [Required]
    public int FlatId { get; set; }
    
    [Required]
    public ResidentType Type { get; set; }
    
    public bool IsPrimaryContact { get; set; }
    
    public DateTime StartDate { get; set; } = DateTime.UtcNow;
}

public class LoginDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty;
}

public class AuthResponseDto
{
    public string Token { get; set; } = string.Empty;
    public UserDto User { get; set; } = new();
    public DateTime ExpiresAt { get; set; }
}
using AMS.API.Attributes;
using AMS.Shared.DTOs;
using AMS.Shared.Enums;
using AMS.Shared.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace AMS.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FlatsController : ControllerBase
{
    private readonly IPropertyService _propertyService;
    private readonly IUserService _userService;

    public FlatsController(IPropertyService propertyService, IUserService userService)
    {
        _propertyService = propertyService;
        _userService = userService;
    }

    /// <summary>
    /// Get flat by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<FlatDto>> GetFlat(int id)
    {
        try
        {
            var flat = await _propertyService.GetFlatByIdAsync(id);
            if (flat == null)
            {
                return NotFound(new { message = $"Flat with ID {id} not found" });
            }
            return Ok(flat);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving the flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Search flats with criteria
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<IEnumerable<FlatDto>>> SearchFlats([FromQuery] FlatSearchCriteria criteria)
    {
        try
        {
            var flats = await _propertyService.SearchFlatsAsync(criteria);
            return Ok(flats);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while searching flats", details = ex.Message });
        }
    }

    /// <summary>
    /// Create a new flat
    /// </summary>
    [HttpPost]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<FlatDto>> CreateFlat([FromBody] CreateFlatDto createFlatDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var flat = await _propertyService.CreateFlatAsync(createFlatDto);
            return CreatedAtAction(nameof(GetFlat), new { id = flat.Id }, flat);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while creating the flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Update an existing flat
    /// </summary>
    [HttpPut("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult<FlatDto>> UpdateFlat(int id, [FromBody] UpdateFlatDto updateFlatDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var flat = await _propertyService.UpdateFlatAsync(id, updateFlatDto);
            if (flat == null)
            {
                return NotFound(new { message = $"Flat with ID {id} not found" });
            }

            return Ok(flat);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while updating the flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete a flat
    /// </summary>
    [HttpDelete("{id}")]
    [AuthorizeRole(UserRole.SuperAdmin, UserRole.SocietyManager)]
    public async Task<ActionResult> DeleteFlat(int id)
    {
        try
        {
            var result = await _propertyService.DeleteFlatAsync(id);
            if (!result)
            {
                return NotFound(new { message = $"Flat with ID {id} not found" });
            }

            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while deleting the flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Get residents of a specific flat
    /// </summary>
    [HttpGet("{id}/residents")]
    public async Task<ActionResult<IEnumerable<UserDto>>> GetFlatResidents(int id)
    {
        try
        {
            // First verify flat exists
            var flat = await _propertyService.GetFlatByIdAsync(id);
            if (flat == null)
            {
                return NotFound(new { message = $"Flat with ID {id} not found" });
            }

            var residents = await _userService.GetFlatResidentsAsync(id);
            return Ok(residents);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while retrieving flat residents", details = ex.Message });
        }
    }

    /// <summary>
    /// Assign a user to a flat
    /// </summary>
    [HttpPost("{id}/residents")]
    public async Task<ActionResult> AssignUserToFlat(int id, [FromBody] AssignUserToFlatDto assignDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Ensure the flat ID in the URL matches the DTO
            if (id != assignDto.FlatId)
            {
                return BadRequest(new { message = "Flat ID in URL does not match the request body" });
            }

            var result = await _userService.AssignUserToFlatAsync(assignDto);
            if (!result)
            {
                return BadRequest(new { message = "Failed to assign user to flat. User or flat may not exist." });
            }

            return Ok(new { message = "User successfully assigned to flat" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while assigning user to flat", details = ex.Message });
        }
    }

    /// <summary>
    /// Remove a user from a flat
    /// </summary>
    [HttpDelete("{flatId}/residents/{userId}")]
    public async Task<ActionResult> RemoveUserFromFlat(int flatId, int userId)
    {
        try
        {
            var result = await _userService.RemoveUserFromFlatAsync(userId, flatId);
            if (!result)
            {
                return NotFound(new { message = "User assignment to flat not found" });
            }

            return Ok(new { message = "User successfully removed from flat" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "An error occurred while removing user from flat", details = ex.Message });
        }
    }
}
using AMS.Shared.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using System.Text.Json;

namespace AMS.Web.Controllers;

public class BlocksController : Controller
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public BlocksController(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        
        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7001";
        _httpClient.BaseAddress = new Uri(apiBaseUrl);
    }

    // GET: Blocks/Details/5
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/blocks/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var block = JsonSerializer.Deserialize<BlockDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(block);
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load block details";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // GET: Blocks/Create?societyId=5
    public async Task<IActionResult> Create(int societyId)
    {
        try
        {
            // Get society details to display context
            var societyResponse = await _httpClient.GetAsync($"/api/societies/{societyId}");
            if (societyResponse.IsSuccessStatusCode)
            {
                var societyJson = await societyResponse.Content.ReadAsStringAsync();
                var society = JsonSerializer.Deserialize<SocietyDto>(societyJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                ViewBag.Society = society;
            }
            
            var createDto = new CreateBlockDto { SocietyId = societyId };
            return View(createDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View(new CreateBlockDto { SocietyId = societyId });
        }
    }

    // POST: Blocks/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateBlockDto createBlockDto)
    {
        if (!ModelState.IsValid)
        {
            // Reload society info for display
            try
            {
                var societyResponse = await _httpClient.GetAsync($"/api/societies/{createBlockDto.SocietyId}");
                if (societyResponse.IsSuccessStatusCode)
                {
                    var societyJson = await societyResponse.Content.ReadAsStringAsync();
                    var society = JsonSerializer.Deserialize<SocietyDto>(societyJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    ViewBag.Society = society;
                }
            }
            catch { }
            
            return View(createBlockDto);
        }

        try
        {
            var json = JsonSerializer.Serialize(createBlockDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/blocks", content);
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Block created successfully";
                return RedirectToAction("Blocks", "Societies", new { id = createBlockDto.SocietyId });
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            ViewBag.Error = $"Failed to create block: {errorContent}";
            return View(createBlockDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View(createBlockDto);
        }
    }

    // GET: Blocks/Edit/5
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/blocks/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var block = JsonSerializer.Deserialize<BlockDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                if (block != null)
                {
                    var updateDto = new UpdateBlockDto
                    {
                        Name = block.Name,
                        NumberOfFloors = block.NumberOfFloors,
                        TotalFlats = block.TotalFlats
                    };
                    ViewBag.BlockId = id;
                    ViewBag.SocietyId = block.SocietyId;
                    ViewBag.SocietyName = block.SocietyName;
                    return View(updateDto);
                }
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load block for editing";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // POST: Blocks/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, UpdateBlockDto updateBlockDto)
    {
        if (!ModelState.IsValid)
        {
            ViewBag.BlockId = id;
            return View(updateBlockDto);
        }

        try
        {
            var json = JsonSerializer.Serialize(updateBlockDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"/api/blocks/{id}", content);
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Block updated successfully";
                
                // Get society ID to redirect back to society blocks
                var blockResponse = await _httpClient.GetAsync($"/api/blocks/{id}");
                if (blockResponse.IsSuccessStatusCode)
                {
                    var blockJson = await blockResponse.Content.ReadAsStringAsync();
                    var block = JsonSerializer.Deserialize<BlockDto>(blockJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return RedirectToAction("Blocks", "Societies", new { id = block?.SocietyId });
                }
                
                return RedirectToAction("Index", "Societies");
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            ViewBag.Error = $"Failed to update block: {errorContent}";
            ViewBag.BlockId = id;
            return View(updateBlockDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            ViewBag.BlockId = id;
            return View(updateBlockDto);
        }
    }

    // GET: Blocks/Delete/5
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/blocks/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var block = JsonSerializer.Deserialize<BlockDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(block);
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load block for deletion";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // POST: Blocks/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            // Get society ID before deletion
            int? societyId = null;
            var blockResponse = await _httpClient.GetAsync($"/api/blocks/{id}");
            if (blockResponse.IsSuccessStatusCode)
            {
                var blockJson = await blockResponse.Content.ReadAsStringAsync();
                var block = JsonSerializer.Deserialize<BlockDto>(blockJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                societyId = block?.SocietyId;
            }
            
            var response = await _httpClient.DeleteAsync($"/api/blocks/{id}");
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Block deleted successfully";
                if (societyId.HasValue)
                {
                    return RedirectToAction("Blocks", "Societies", new { id = societyId.Value });
                }
                return RedirectToAction("Index", "Societies");
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            TempData["Error"] = $"Failed to delete block: {errorContent}";
            return RedirectToAction(nameof(Delete), new { id });
        }
        catch (Exception ex)
        {
            TempData["Error"] = $"Error: {ex.Message}";
            return RedirectToAction(nameof(Delete), new { id });
        }
    }

    // GET: Blocks/5/Flats
    public async Task<IActionResult> Flats(int id)
    {
        try
        {
            // Get block details first
            var blockResponse = await _httpClient.GetAsync($"/api/blocks/{id}");
            if (!blockResponse.IsSuccessStatusCode)
            {
                return NotFound();
            }
            
            var blockJson = await blockResponse.Content.ReadAsStringAsync();
            var block = JsonSerializer.Deserialize<BlockDto>(blockJson, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            // Get flats for the block
            var flatsResponse = await _httpClient.GetAsync($"/api/blocks/{id}/flats");
            if (flatsResponse.IsSuccessStatusCode)
            {
                var flatsJson = await flatsResponse.Content.ReadAsStringAsync();
                var flats = JsonSerializer.Deserialize<List<FlatDto>>(flatsJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                ViewBag.Block = block;
                return View(flats ?? new List<FlatDto>());
            }
            
            ViewBag.Error = "Failed to load flats";
            ViewBag.Block = block;
            return View(new List<FlatDto>());
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View(new List<FlatDto>());
        }
    }
}
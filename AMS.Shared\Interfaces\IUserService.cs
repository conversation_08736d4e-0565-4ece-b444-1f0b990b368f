using AMS.Shared.DTOs;
using AMS.Shared.Enums;

namespace AMS.Shared.Interfaces;

public interface IUserService
{
    Task<UserDto> CreateUserAsync(CreateUserDto dto);
    Task<UserDto?> GetUserByIdAsync(int id);
    Task<UserDto?> GetUserByEmailAsync(string email);
    Task<UserDto?> UpdateUserAsync(int id, UpdateUserDto dto);
    Task<bool> AssignUserToFlatAsync(AssignUserToFlatDto dto);
    Task<bool> RemoveUserFromFlatAsync(int userId, int flatId);
    Task<IEnumerable<UserDto>> GetFlatResidentsAsync(int flatId);
    Task<AuthResponseDto?> AuthenticateAsync(LoginDto loginDto);
    Task<IEnumerable<UserDto>> GetAllUsersAsync();
    Task<bool> DeactivateUserAsync(int id);
}
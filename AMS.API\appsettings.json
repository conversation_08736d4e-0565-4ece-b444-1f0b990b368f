{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=.;Database=AMSERP;User ID=sa;Password=***;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForJWTTokenGeneration!", "Issuer": "AMS.API", "Audience": "AMS.Client", "ExpiryMinutes": 1440}, "AllowedHosts": "*"}
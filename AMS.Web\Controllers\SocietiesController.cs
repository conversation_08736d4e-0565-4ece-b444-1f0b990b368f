using AMS.Shared.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Text;
using System.Text.Json;

namespace AMS.Web.Controllers;

public class SocietiesController : Controller
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public SocietiesController(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        
        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7001";
        _httpClient.BaseAddress = new Uri(apiBaseUrl);
    }

    // GET: Societies
    public async Task<IActionResult> Index()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/societies");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var societies = JsonSerializer.Deserialize<List<SocietyDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(societies ?? new List<SocietyDto>());
            }
            
            ViewBag.Error = "Failed to load societies";
            return View(new List<SocietyDto>());
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View(new List<SocietyDto>());
        }
    }

    // GET: Societies/Details/5
    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/societies/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var society = JsonSerializer.Deserialize<SocietyDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(society);
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load society details";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // GET: Societies/Create
    public IActionResult Create()
    {
        return View();
    }

    // POST: Societies/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateSocietyDto createSocietyDto)
    {
        if (!ModelState.IsValid)
        {
            return View(createSocietyDto);
        }

        try
        {
            var json = JsonSerializer.Serialize(createSocietyDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/societies", content);
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Society created successfully";
                return RedirectToAction(nameof(Index));
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            ViewBag.Error = $"Failed to create society: {errorContent}";
            return View(createSocietyDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View(createSocietyDto);
        }
    }

    // GET: Societies/Edit/5
    public async Task<IActionResult> Edit(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/societies/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var society = JsonSerializer.Deserialize<SocietyDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                if (society != null)
                {
                    var updateDto = new UpdateSocietyDto
                    {
                        Name = society.Name,
                        Address = society.Address,
                        ContactEmail = society.ContactEmail,
                        ContactPhone = society.ContactPhone
                    };
                    ViewBag.SocietyId = id;
                    return View(updateDto);
                }
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load society for editing";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // POST: Societies/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, UpdateSocietyDto updateSocietyDto)
    {
        if (!ModelState.IsValid)
        {
            ViewBag.SocietyId = id;
            return View(updateSocietyDto);
        }

        try
        {
            var json = JsonSerializer.Serialize(updateSocietyDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"/api/societies/{id}", content);
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Society updated successfully";
                return RedirectToAction(nameof(Index));
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            ViewBag.Error = $"Failed to update society: {errorContent}";
            ViewBag.SocietyId = id;
            return View(updateSocietyDto);
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            ViewBag.SocietyId = id;
            return View(updateSocietyDto);
        }
    }

    // GET: Societies/Delete/5
    public async Task<IActionResult> Delete(int id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/societies/{id}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var society = JsonSerializer.Deserialize<SocietyDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return View(society);
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return NotFound();
            }
            
            ViewBag.Error = "Failed to load society for deletion";
            return View();
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View();
        }
    }

    // POST: Societies/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"/api/societies/{id}");
            if (response.IsSuccessStatusCode)
            {
                TempData["Success"] = "Society deleted successfully";
                return RedirectToAction(nameof(Index));
            }
            
            var errorContent = await response.Content.ReadAsStringAsync();
            TempData["Error"] = $"Failed to delete society: {errorContent}";
            return RedirectToAction(nameof(Delete), new { id });
        }
        catch (Exception ex)
        {
            TempData["Error"] = $"Error: {ex.Message}";
            return RedirectToAction(nameof(Delete), new { id });
        }
    }

    // GET: Societies/5/Blocks
    public async Task<IActionResult> Blocks(int id)
    {
        try
        {
            // Get society details first
            var societyResponse = await _httpClient.GetAsync($"/api/societies/{id}");
            if (!societyResponse.IsSuccessStatusCode)
            {
                return NotFound();
            }
            
            var societyJson = await societyResponse.Content.ReadAsStringAsync();
            var society = JsonSerializer.Deserialize<SocietyDto>(societyJson, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            // Get blocks for the society
            var blocksResponse = await _httpClient.GetAsync($"/api/societies/{id}/blocks");
            if (blocksResponse.IsSuccessStatusCode)
            {
                var blocksJson = await blocksResponse.Content.ReadAsStringAsync();
                var blocks = JsonSerializer.Deserialize<List<BlockDto>>(blocksJson, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                ViewBag.Society = society;
                return View(blocks ?? new List<BlockDto>());
            }
            
            ViewBag.Error = "Failed to load blocks";
            ViewBag.Society = society;
            return View(new List<BlockDto>());
        }
        catch (Exception ex)
        {
            ViewBag.Error = $"Error: {ex.Message}";
            return View(new List<BlockDto>());
        }
    }
}
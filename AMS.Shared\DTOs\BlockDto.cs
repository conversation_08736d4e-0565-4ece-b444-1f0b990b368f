using System.ComponentModel.DataAnnotations;

namespace AMS.Shared.DTOs;

public class BlockDto
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [Range(1, 100)]
    public int NumberOfFloors { get; set; }
    
    [Required]
    [Range(1, 1000)]
    public int TotalFlats { get; set; }
    
    public int SocietyId { get; set; }
    public string SocietyName { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public int OccupiedFlats { get; set; }
    public int VacantFlats { get; set; }
}

public class CreateBlockDto
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [Range(1, 100)]
    public int NumberOfFloors { get; set; }
    
    [Required]
    [Range(1, 1000)]
    public int TotalFlats { get; set; }
    
    [Required]
    public int SocietyId { get; set; }
}

public class UpdateBlockDto
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [Range(1, 100)]
    public int NumberOfFloors { get; set; }
    
    [Required]
    [Range(1, 1000)]
    public int TotalFlats { get; set; }
}